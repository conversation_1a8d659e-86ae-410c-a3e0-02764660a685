<?php

namespace App\Services;

use App\Models\Project;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProjectService
{
    /**
     * Get all projects for a user with pagination.
     */
    public function getUserProjects(User $user, int $perPage = 15): LengthAwarePaginator
    {
        return $user->projects()
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get all projects for a user without pagination.
     */
    public function getAllUserProjects(User $user): Collection
    {
        return $user->projects()
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Create a new project for a user.
     */
    public function createProject(User $user, array $data): Project
    {
        $validatedData = $this->validateProjectData($data);
        $validatedData['user_id'] = $user->id;

        return Project::create($validatedData);
    }

    /**
     * Update an existing project.
     */
    public function updateProject(Project $project, array $data): Project
    {
        $validatedData = $this->validateProjectData($data);
        
        $project->update($validatedData);
        
        return $project->fresh();
    }

    /**
     * Delete a project.
     */
    public function deleteProject(Project $project): bool
    {
        return $project->delete();
    }

    /**
     * Find a project by ID for a specific user.
     */
    public function findUserProject(User $user, int $projectId): ?Project
    {
        return $user->projects()->find($projectId);
    }

    /**
     * Validate project data.
     */
    private function validateProjectData(array $data): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:255',
        ];

        return validator($data, $rules)->validate();
    }
}
