import Heading from '@/components/heading';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppSidebarLayout from '@/layouts/app/app-sidebar-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, ExternalLink, Pencil, Trash2 } from 'lucide-react';
import { type ReactElement } from 'react';

interface Project {
    id: number;
    name: string;
    url: string;
    created_at: string;
    updated_at: string;
}

interface Props {
    project: Project;
}

export default function ShowProject({ project }: Props) {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Projects', href: '/projects' },
        { title: project.name, href: `/projects/${project.id}` },
    ];

    const handleDelete = () => {
        if (confirm(`Are you sure you want to delete "${project.name}"?`)) {
            router.delete(`/projects/${project.id}`);
        }
    };

    return (
        <>
            <Head title={project.name} />
            
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" asChild>
                        <Link href="/projects">
                            <ArrowLeft className="h-4 w-4" />
                        </Link>
                    </Button>
                    <Heading level={1}>{project.name}</Heading>
                </div>

                <div className="flex items-center gap-4">
                    <Button asChild>
                        <Link href={`/projects/${project.id}/edit`}>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit Project
                        </Link>
                    </Button>
                    <Button
                        variant="destructive"
                        onClick={handleDelete}
                    >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Project
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Project Details</CardTitle>
                        <CardDescription>
                            Information about this project.
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div>
                            <h3 className="font-medium text-sm text-muted-foreground mb-1">Name</h3>
                            <p className="text-lg">{project.name}</p>
                        </div>
                        
                        <div>
                            <h3 className="font-medium text-sm text-muted-foreground mb-1">URL</h3>
                            <a
                                href={project.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center text-blue-600 hover:text-blue-800 text-lg"
                            >
                                {project.url}
                                <ExternalLink className="ml-2 h-4 w-4" />
                            </a>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 pt-4">
                            <div>
                                <h3 className="font-medium text-sm text-muted-foreground mb-1">Created</h3>
                                <p>{new Date(project.created_at).toLocaleDateString()}</p>
                            </div>
                            <div>
                                <h3 className="font-medium text-sm text-muted-foreground mb-1">Last Updated</h3>
                                <p>{new Date(project.updated_at).toLocaleDateString()}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </>
    );
}

ShowProject.layout = (page: ReactElement) => (
    <AppSidebarLayout breadcrumbs={breadcrumbs}>{page}</AppSidebarLayout>
);
